import openai
import json
import requests
from typing import Dict, Any, Optional

class CustomOpenAIClient:
    """自定义OpenAI客户端，支持自定义API端点"""
    
    def __init__(self, api_key: str, base_url: str, model: str = "gpt-3.5-turbo"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def chat_completion(self, messages: list, tools: Optional[list] = None, 
                       tool_choice: str = "auto") -> Dict[str, Any]:
        """发送聊天完成请求"""
        url = f"{self.base_url}/v1/chat/completions"
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = tool_choice
        
        try:
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {str(e)}")

# 配置自定义API端点
API_CONFIGS = {
    "openai_official": {
        "base_url": "http://localhost:8333",
        "api_key": "sk-my-secret-key-1",
        "model": "claude-sonnet-4-20250514"
    },
    "azure_openai": {
        "base_url": "https://your-resource.openai.azure.com",
        "api_key": "your-azure-api-key",
        "model": "gpt-35-turbo"
    },
    "local_api": {
        "base_url": "http://localhost:8000",
        "api_key": "local-api-key",
        "model": "local-model"
    },
    "third_party": {
        "base_url": "https://api.third-party.com",
        "api_key": "your-third-party-key",
        "model": "custom-model"
    }
}

# 工具函数定义
def get_weather(location: str, unit: str = "celsius") -> Dict[str, Any]:
    """获取指定位置的天气信息"""
    weather_data = {
        "location": location,
        "temperature": 22 if unit == "celsius" else 72,
        "unit": unit,
        "condition": "晴朗",
        "humidity": "65%",
        "wind_speed": "10 km/h",
        "timestamp": "2024-01-15 14:30:00"
    }
    return weather_data

def calculate_math(expression: str) -> Dict[str, Any]:
    """安全的数学计算"""
    import math
    import operator
    
    # 允许的操作符和函数
    allowed_operators = {
        '+': operator.add,
        '-': operator.sub,
        '*': operator.mul,
        '/': operator.truediv,
        '**': operator.pow,
        'sqrt': math.sqrt,
        'sin': math.sin,
        'cos': math.cos,
        'tan': math.tan,
        'log': math.log,
        'pi': math.pi,
        'e': math.e
    }
    
    try:
        # 简单的表达式求值（生产环境中应使用更安全的方法）
        result = eval(expression, {"__builtins__": {}}, allowed_operators)
        return {
            "expression": expression,
            "result": result,
            "status": "success"
        }
    except Exception as e:
        return {
            "expression": expression,
            "error": str(e),
            "status": "error"
        }

def get_current_time(timezone: str = "UTC") -> Dict[str, Any]:
    """获取当前时间"""
    from datetime import datetime
    import pytz
    
    try:
        tz = pytz.timezone(timezone)
        current_time = datetime.now(tz)
        return {
            "timezone": timezone,
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S %Z"),
            "timestamp": current_time.timestamp(),
            "status": "success"
        }
    except Exception as e:
        return {
            "timezone": timezone,
            "error": str(e),
            "status": "error"
        }

def search_database(query: str, table: str = "products") -> Dict[str, Any]:
    """模拟数据库搜索"""
    # 模拟数据库结果
    mock_results = {
        "products": [
            {"id": 1, "name": f"产品A-{query}", "price": 99.99, "stock": 50},
            {"id": 2, "name": f"产品B-{query}", "price": 149.99, "stock": 30},
            {"id": 3, "name": f"产品C-{query}", "price": 199.99, "stock": 20}
        ],
        "users": [
            {"id": 1, "name": f"用户-{query}", "email": f"{query}@example.com", "status": "active"},
            {"id": 2, "name": f"管理员-{query}", "email": f"admin-{query}@example.com", "status": "active"}
        ]
    }
    
    return {
        "query": query,
        "table": table,
        "results": mock_results.get(table, []),
        "count": len(mock_results.get(table, [])),
        "status": "success"
    }

# 工具描述定义
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定位置的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "城市名称，例如：北京、上海、New York"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度单位，默认为摄氏度"
                    }
                },
                "required": ["location"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "calculate_math",
            "description": "计算数学表达式，支持基本运算和常用数学函数",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "数学表达式，例如：2+2, sqrt(16), sin(pi/2)"
                    }
                },
                "required": ["expression"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "获取指定时区的当前时间",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "时区名称，例如：UTC, Asia/Shanghai, America/New_York",
                        "default": "UTC"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_database",
            "description": "在数据库中搜索信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "table": {
                        "type": "string",
                        "enum": ["products", "users"],
                        "description": "要搜索的表名",
                        "default": "products"
                    }
                },
                "required": ["query"]
            }
        }
    }
]

# 工具函数映射
available_functions = {
    "get_weather": get_weather,
    "calculate_math": calculate_math,
    "get_current_time": get_current_time,
    "search_database": search_database
}

class ToolCallingTester:
    """工具调用测试器"""
    
    def __init__(self, config_name: str):
        if config_name not in API_CONFIGS:
            raise ValueError(f"未知的配置: {config_name}")
        
        config = API_CONFIGS[config_name]
        self.client = CustomOpenAIClient(
            api_key=config["api_key"],
            base_url=config["base_url"],
            model=config["model"]
        )
        self.config_name = config_name
    
    def test_tool_calling(self, user_message: str, verbose: bool = True):
        """测试工具调用功能"""
        if verbose:
            print(f"\n使用配置: {self.config_name}")
            print(f"用户输入: {user_message}")
            print("-" * 60)
        
        try:
            # 第一次请求
            messages = [
                {"role": "user", "content": user_message}
            ]

            response = self.client.chat_completion(
                messages=messages,
                tools=tools,
                tool_choice="auto"
            )
            
            if "choices" not in response or not response["choices"]:
                raise Exception("API响应格式错误")
            
            message = response["choices"][0]["message"]
            
            # 检查是否需要调用工具
            if "tool_calls" in message and message["tool_calls"]:
                if verbose:
                    print("AI决定调用工具:")
                
                # 添加助手消息到对话历史
                messages.append(message)
                
                # 处理每个工具调用
                for tool_call in message["tool_calls"]:
                    function_name = tool_call["function"]["name"]
                    function_args = json.loads(tool_call["function"]["arguments"])
                    
                    if verbose:
                        print(f"调用函数: {function_name}")
                        print(f"参数: {json.dumps(function_args, ensure_ascii=False, indent=2)}")
                    
                    # 执行函数
                    if function_name in available_functions:
                        try:
                            function_result = available_functions[function_name](**function_args)
                            if verbose:
                                print(f"函数结果: {json.dumps(function_result, ensure_ascii=False, indent=2)}")
                            
                            # 添加工具调用结果到对话历史
                            messages.append({
                                "role": "tool",
                                "tool_call_id": tool_call["id"],
                                "content": json.dumps(function_result, ensure_ascii=False)
                            })
                            
                        except Exception as e:
                            error_result = {"error": str(e), "status": "function_error"}
                            messages.append({
                                "role": "tool",
                                "tool_call_id": tool_call["id"],
                                "content": json.dumps(error_result, ensure_ascii=False)
                            })
                            if verbose:
                                print(f"函数执行错误: {str(e)}")
                    else:
                        error_result = {"error": f"未找到函数 {function_name}", "status": "function_not_found"}
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": json.dumps(error_result, ensure_ascii=False)
                        })
                
                # 获取最终回复
                final_response = self.client.chat_completion(messages=messages)
                final_message = final_response["choices"][0]["message"]["content"]
                
                if verbose:
                    print(f"\nAI最终回复: {final_message}")
                
                return {
                    "status": "success",
                    "tool_calls": len(message["tool_calls"]),
                    "final_response": final_message
                }
            else:
                if verbose:
                    print(f"AI直接回复: {message['content']}")
                
                return {
                    "status": "success",
                    "tool_calls": 0,
                    "final_response": message["content"]
                }
                
        except Exception as e:
            error_msg = f"错误: {str(e)}"
            if verbose:
                print(error_msg)
            return {
                "status": "error",
                "error": error_msg
            }

def run_comprehensive_tests():
    """运行综合测试"""
    test_cases = [
        "现在北京时间是几点？",
        "搜索产品数据库中关于'手机'的信息",
        "计算 sin(pi/2) + cos(0)",
    ]
    
    # 选择要测试的配置
    config_to_test = "openai_official"  # 可以改为其他配置
    
    print(f"OpenAI工具调用测试程序 - 使用自定义API端点")
    print(f"当前配置: {config_to_test}")
    print("=" * 80)
    
    try:
        tester = ToolCallingTester(config_to_test)
        
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}:")
            result = tester.test_tool_calling(test_case)
            results.append({"case": test_case, "result": result})
            print("=" * 80)
        
        # 测试总结
        print("\n测试总结:")
        print("-" * 40)
        success_count = sum(1 for r in results if r["result"]["status"] == "success")
        tool_calls_count = sum(r["result"].get("tool_calls", 0) for r in results)
        
        print(f"总测试用例: {len(results)}")
        print(f"成功: {success_count}")
        print(f"失败: {len(results) - success_count}")
        print(f"工具调用次数: {tool_calls_count}")
        
    except Exception as e:
        print(f"测试初始化失败: {str(e)}")

def interactive_test():
    """交互式测试"""
    print("\n可用的API配置:")
    for i, config_name in enumerate(API_CONFIGS.keys(), 1):
        print(f"{i}. {config_name}")
    
    while True:
        try:
            choice = input("\n选择API配置 (输入数字或配置名): ").strip()
            if choice.isdigit():
                config_names = list(API_CONFIGS.keys())
                if 1 <= int(choice) <= len(config_names):
                    config_name = config_names[int(choice) - 1]
                    break
            elif choice in API_CONFIGS:
                config_name = choice
                break
            else:
                print("无效选择，请重试")
        except KeyboardInterrupt:
            return
    
    try:
        tester = ToolCallingTester(config_name)
        print(f"\n使用配置: {config_name}")
        print("进入交互模式 (输入 'quit' 退出):")
        
        while True:
            try:
                user_input = input("\n请输入您的问题: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                if user_input:
                    tester.test_tool_calling(user_input)
            except KeyboardInterrupt:
                break
                
    except Exception as e:
        print(f"交互测试初始化失败: {str(e)}")

if __name__ == "__main__":
    print("请确保已安装必要的依赖:")
    print("pip install requests pytz")
    print("\n请在API_CONFIGS中配置您的API端点信息")
    
    # 运行预定义测试用例
    run_comprehensive_tests()
    
    # 可选：运行交互式测试
    choice = input("\n是否进入交互模式？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        interactive_test()
